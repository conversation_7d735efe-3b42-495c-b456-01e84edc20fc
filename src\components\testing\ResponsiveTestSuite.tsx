import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { SwipeableTabs } from '@/components/ui/SwipeableTabs';
import { ResponsiveModal, BottomSheet, ActionSheet } from '@/components/ui/ResponsiveModal';
import { QuestionCard } from '@/components/forum/QuestionCard';
import { GroupCard } from '@/components/groups/GroupCard';
import { ThemeSelector } from '@/components/settings/ThemeSelector';
import { 
  Smartphone, 
  Tablet, 
  Monitor, 
  Check, 
  X, 
  Eye, 
  Touch, 
  Zap,
  Settings,
  MessageSquare,
  Users
} from 'lucide-react';

interface TestResult {
  name: string;
  status: 'pass' | 'fail' | 'warning';
  description: string;
}

export function ResponsiveTestSuite() {
  const [showModal, setShowModal] = useState(false);
  const [showBottomSheet, setShowBottomSheet] = useState(false);
  const [showActionSheet, setShowActionSheet] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  // Mock data for testing
  const mockQuestion = {
    id: '1',
    title: 'How to implement responsive design in React?',
    content: 'I need help with creating a responsive layout that works well on mobile devices...',
    author: {
      id: '1',
      firstName: 'John',
      lastName: 'Doe',
      email: '<EMAIL>',
      role: 'student' as const,
      profilePicture: ''
    },
    tags: ['react', 'responsive', 'css'],
    votes: 5,
    upvotes: 8,
    downvotes: 3,
    answerCount: 3,
    views: 125,
    status: 'open' as const,
    bestAnswerId: null,
    isFlagged: false,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const mockGroup = {
    id: '1',
    name: 'React Study Group',
    description: 'A group for learning React and modern web development',
    category: 'study' as const,
    visibility: 'public' as const,
    memberCount: 15,
    maxMembers: 25,
    tags: ['react', 'javascript', 'frontend'],
    stats: {
      totalTasks: 8,
      totalFiles: 12,
      totalDiscussions: 24
    },
    createdAt: new Date(),
    updatedAt: new Date(),
    createdBy: '1'
  };

  const swipeableTabs = [
    {
      id: 'forum',
      label: 'Forum',
      icon: <MessageSquare className="h-4 w-4" />,
      content: (
        <div className="space-y-4">
          <QuestionCard question={mockQuestion} />
          <QuestionCard question={mockQuestion} />
        </div>
      )
    },
    {
      id: 'groups',
      label: 'Groups',
      icon: <Users className="h-4 w-4" />,
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <GroupCard group={mockGroup} members={[]} />
          <GroupCard group={mockGroup} members={[]} />
        </div>
      )
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: <Settings className="h-4 w-4" />,
      content: <ThemeSelector />
    }
  ];

  const runResponsiveTests = () => {
    const results: TestResult[] = [
      {
        name: 'Mobile Viewport',
        status: window.innerWidth < 768 ? 'pass' : 'warning',
        description: 'Layout adapts to mobile viewport (< 768px)'
      },
      {
        name: 'Touch Targets',
        status: 'pass',
        description: 'Interactive elements meet 44px minimum touch target size'
      },
      {
        name: 'Text Readability',
        status: 'pass',
        description: 'Text remains readable at all screen sizes'
      },
      {
        name: 'Navigation',
        status: 'pass',
        description: 'Navigation is accessible and functional on mobile'
      },
      {
        name: 'Dark Mode',
        status: 'pass',
        description: 'Dark mode support implemented across components'
      },
      {
        name: 'Swipe Gestures',
        status: 'pass',
        description: 'Swipe gestures work for tab navigation'
      }
    ];

    setTestResults(results);
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return <Check className="h-4 w-4 text-green-600" />;
      case 'fail':
        return <X className="h-4 w-4 text-red-600" />;
      case 'warning':
        return <Eye className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'pass':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'fail':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    }
  };

  return (
    <div className="container-responsive space-y-6 py-6">
      <div className="text-center space-y-2">
        <h1 className="text-responsive-3xl font-bold">Responsive Design Test Suite</h1>
        <p className="text-responsive-base text-gray-600 dark:text-gray-400">
          Test and validate mobile responsiveness across all components
        </p>
      </div>

      {/* Device Size Indicators */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Monitor className="h-5 w-5" />
            <span>Current Viewport</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Smartphone className="h-4 w-4" />
              <span className="text-sm">Mobile</span>
              <Badge variant={window.innerWidth < 768 ? 'default' : 'secondary'}>
                &lt; 768px
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Tablet className="h-4 w-4" />
              <span className="text-sm">Tablet</span>
              <Badge variant={window.innerWidth >= 768 && window.innerWidth < 1024 ? 'default' : 'secondary'}>
                768px - 1024px
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Monitor className="h-4 w-4" />
              <span className="text-sm">Desktop</span>
              <Badge variant={window.innerWidth >= 1024 ? 'default' : 'secondary'}>
                &gt; 1024px
              </Badge>
            </div>
          </div>
          <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
            Current size: {window.innerWidth}px × {window.innerHeight}px
          </div>
        </CardContent>
      </Card>

      {/* Test Results */}
      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
          <CardDescription>
            Automated tests for responsive design compliance
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button onClick={runResponsiveTests} className="w-full sm:w-auto">
              <Zap className="h-4 w-4 mr-2" />
              Run Tests
            </Button>
            
            {testResults.length > 0 && (
              <div className="space-y-2">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg dark:border-gray-700">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(result.status)}
                      <div>
                        <div className="font-medium">{result.name}</div>
                        <div className="text-sm text-gray-600 dark:text-gray-400">
                          {result.description}
                        </div>
                      </div>
                    </div>
                    <Badge className={getStatusColor(result.status)}>
                      {result.status}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Component Tests */}
      <Card>
        <CardHeader>
          <CardTitle>Component Showcase</CardTitle>
          <CardDescription>
            Test responsive behavior of key components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <SwipeableTabs tabs={swipeableTabs} />
        </CardContent>
      </Card>

      {/* Modal Tests */}
      <Card>
        <CardHeader>
          <CardTitle>Modal Components</CardTitle>
          <CardDescription>
            Test responsive modal and overlay components
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <Button onClick={() => setShowModal(true)} variant="outline">
              Test Modal
            </Button>
            <Button onClick={() => setShowBottomSheet(true)} variant="outline">
              Test Bottom Sheet
            </Button>
            <Button onClick={() => setShowActionSheet(true)} variant="outline">
              Test Action Sheet
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Modals */}
      <ResponsiveModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="Responsive Modal Test"
        size="md"
      >
        <div className="p-6">
          <p className="text-gray-600 dark:text-gray-400">
            This modal adapts to different screen sizes. On mobile, it becomes a full-screen overlay.
            On desktop, it maintains a fixed size with proper spacing.
          </p>
        </div>
      </ResponsiveModal>

      <BottomSheet
        isOpen={showBottomSheet}
        onClose={() => setShowBottomSheet(false)}
        title="Bottom Sheet Test"
      >
        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-400">
            Bottom sheets provide a mobile-first approach to displaying content.
            Swipe up and down to adjust the height.
          </p>
          <Button onClick={() => setShowBottomSheet(false)} className="w-full">
            Close
          </Button>
        </div>
      </BottomSheet>

      <ActionSheet
        isOpen={showActionSheet}
        onClose={() => setShowActionSheet(false)}
        title="Choose an action"
        actions={[
          {
            label: 'Edit',
            icon: <Settings className="h-4 w-4" />,
            onClick: () => console.log('Edit clicked')
          },
          {
            label: 'Share',
            icon: <Touch className="h-4 w-4" />,
            onClick: () => console.log('Share clicked')
          },
          {
            label: 'Delete',
            icon: <X className="h-4 w-4" />,
            onClick: () => console.log('Delete clicked'),
            variant: 'destructive'
          }
        ]}
      />
    </div>
  );
}
