/* Responsive Typography and Spacing System */

/* Base responsive typography */
html {
  font-size: 14px;
}

@media (min-width: 640px) {
  html {
    font-size: 16px;
  }
}

/* Responsive text utilities */
.text-responsive-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

@media (min-width: 640px) {
  .text-responsive-xs {
    font-size: 0.875rem;
    line-height: 1.25rem;
  }
}

.text-responsive-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

@media (min-width: 640px) {
  .text-responsive-sm {
    font-size: 1rem;
    line-height: 1.5rem;
  }
}

.text-responsive-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

@media (min-width: 640px) {
  .text-responsive-base {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

.text-responsive-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

@media (min-width: 640px) {
  .text-responsive-lg {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

.text-responsive-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

@media (min-width: 640px) {
  .text-responsive-xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }
}

.text-responsive-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

@media (min-width: 640px) {
  .text-responsive-2xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }
}

.text-responsive-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

@media (min-width: 640px) {
  .text-responsive-3xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }
}

/* Responsive spacing utilities */
.space-responsive-1 > * + * {
  margin-top: 0.25rem;
}

@media (min-width: 640px) {
  .space-responsive-1 > * + * {
    margin-top: 0.5rem;
  }
}

.space-responsive-2 > * + * {
  margin-top: 0.5rem;
}

@media (min-width: 640px) {
  .space-responsive-2 > * + * {
    margin-top: 0.75rem;
  }
}

.space-responsive-3 > * + * {
  margin-top: 0.75rem;
}

@media (min-width: 640px) {
  .space-responsive-3 > * + * {
    margin-top: 1rem;
  }
}

.space-responsive-4 > * + * {
  margin-top: 1rem;
}

@media (min-width: 640px) {
  .space-responsive-4 > * + * {
    margin-top: 1.5rem;
  }
}

.space-responsive-6 > * + * {
  margin-top: 1.5rem;
}

@media (min-width: 640px) {
  .space-responsive-6 > * + * {
    margin-top: 2rem;
  }
}

/* Responsive padding utilities */
.p-responsive-2 {
  padding: 0.5rem;
}

@media (min-width: 640px) {
  .p-responsive-2 {
    padding: 0.75rem;
  }
}

.p-responsive-4 {
  padding: 1rem;
}

@media (min-width: 640px) {
  .p-responsive-4 {
    padding: 1.5rem;
  }
}

.p-responsive-6 {
  padding: 1.5rem;
}

@media (min-width: 640px) {
  .p-responsive-6 {
    padding: 2rem;
  }
}

/* Responsive margin utilities */
.m-responsive-2 {
  margin: 0.5rem;
}

@media (min-width: 640px) {
  .m-responsive-2 {
    margin: 0.75rem;
  }
}

.m-responsive-4 {
  margin: 1rem;
}

@media (min-width: 640px) {
  .m-responsive-4 {
    margin: 1.5rem;
  }
}

/* Touch-friendly interactive elements */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
}

@media (min-width: 640px) {
  .touch-target {
    min-height: 40px;
    min-width: 40px;
  }
}

/* Responsive grid utilities */
.grid-responsive-1 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 640px) {
  .grid-responsive-1 {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .grid-responsive-1 {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
  }
}

.grid-responsive-2 {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
}

@media (min-width: 768px) {
  .grid-responsive-2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .grid-responsive-2 {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
}

/* Responsive flex utilities */
.flex-responsive-col {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

@media (min-width: 640px) {
  .flex-responsive-col {
    flex-direction: row;
    gap: 1.5rem;
  }
}

/* Mobile-first container */
.container-responsive {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 640px) {
  .container-responsive {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .container-responsive {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    max-width: 1024px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .container-responsive {
    max-width: 1280px;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card {
    border-width: 2px;
  }
  
  .button {
    border-width: 2px;
  }
}

/* Focus improvements for keyboard navigation */
.focus-visible:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Dark mode responsive adjustments */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    background-color: #1f2937;
    color: #f9fafb;
  }
}
